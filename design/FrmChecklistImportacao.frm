object FrmChecklistImportacao: TFForm
  Left = 321
  Top = 163
  ActiveControl = vboxPrincipal
  BorderIcons = [biSystemMenu, biMinimize]
  Caption = 'Importar checklist'
  ClientHeight = 464
  ClientWidth = 498
  Color = clBtnFace
  Constraints.MaxHeight = 5000
  Constraints.MaxWidth = 5000
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '342012'
  ShortcutKeys = <>
  InterfaceRN = 'ChecklistImportacaoRN'
  Access = False
  ChangedProp = 
    'FrmChecklistImportacao.Width;'#13#10'FrmChecklistImportacao.Height;'#13#10'F' +
    'rmChecklistImportacao.ActiveControlFrmChecklistImportacao.Width;' +
    #13#10'FrmChecklistImportacao.ActiveControl;'#13#10
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object vboxPrincipal: TFVBox
    Left = 0
    Top = 0
    Width = 498
    Height = 464
    Align = alClient
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 0
    Padding.Left = 0
    Padding.Right = 0
    Padding.Bottom = 0
    TabOrder = 0
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 1
    Flex.Vflex = ftTrue
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    object vboxForm: TFVBox
      Left = 0
      Top = 0
      Width = 496
      Height = 399
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      FlowStyle = fsTopBottomLeftRight
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 0
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftTrue
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object vBoxlblMontadoraPrincipal: TFHBox
        Left = 0
        Top = 0
        Width = 487
        Height = 64
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 5
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 5
        Flex.Vflex = ftFalse
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object vBoxlblMontadoraEspacamento: TFVBox
          Left = 0
          Top = 0
          Width = 17
          Height = 41
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
        end
        object vBoxlblMontadora: TFVBox
          Left = 17
          Top = 0
          Width = 217
          Height = 41
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 1
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object lblMontadora: TFLabel
            Left = 0
            Top = 0
            Width = 71
            Height = 16
            Caption = 'Montadora'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clBlack
            Font.Height = -13
            Font.Name = 'Tahoma'
            Font.Style = [fsBold]
            ParentFont = False
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taVerticalCenter
            WordBreak = False
            MaskType = mtText
          end
          object lblDescricaoMontadora: TFLabel
            Left = 0
            Top = 17
            Width = 96
            Height = 16
            Caption = 'concession'#225'ria'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clRed
            Font.Height = -13
            Font.Name = 'Tahoma'
            Font.Style = [fsBold]
            ParentFont = False
            FieldName = 'DESCRICAO'
            Table = tbConcessionariaTipo
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taVerticalCenter
            WordBreak = False
            MaskType = mtText
          end
        end
      end
      object gpBoxChecklists: TFGroupbox
        Left = 0
        Top = 65
        Width = 489
        Height = 437
        Caption = 'Checklists Dispon'#237'veis'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        ParentFont = False
        TabOrder = 1
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Flex.Vflex = ftTrue
        Flex.Hflex = ftTrue
        WOwner = FrInterno
        WOrigem = EhNone
        Scrollable = False
        Closable = False
        Closed = False
        Orient = coHorizontal
        Style = grp3D
        HeaderImageId = 0
        object gridChecklist: TFGrid
          Left = 2
          Top = 15
          Width = 485
          Height = 420
          Align = alClient
          TabOrder = 0
          TitleFont.Charset = DEFAULT_CHARSET
          TitleFont.Color = clWindowText
          TitleFont.Height = -11
          TitleFont.Name = 'Tahoma'
          TitleFont.Style = []
          Table = tbChecklist
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Paging.Enabled = True
          Paging.PageSize = 0
          Paging.DbPaging = False
          FrozenColumns = 0
          ShowFooter = False
          ShowHeader = True
          MultiSelection = False
          Grouping.Enabled = False
          Grouping.Expanded = False
          Grouping.ShowFooter = False
          Crosstab.Enabled = False
          Crosstab.GroupType = cgtConcat
          EnablePopup = False
          WOwner = FrInterno
          WOrigem = EhNone
          EditionEnabled = True
          AuxColumnHeaders = <>
          NoBorder = False
          ActionButtons.BtnAccept = False
          ActionButtons.BtnView = False
          ActionButtons.BtnEdit = False
          ActionButtons.BtnDelete = False
          ActionButtons.BtnInLineEdit = False
          CustomActionButtons = <>
          ActionColumn.Title = 'A'#231#245'es'
          ActionColumn.Width = 100
          ActionColumn.TextAlign = taCenter
          ActionColumn.Visible = True
          Columns = <
            item
              Expanded = False
              FieldName = 'ID_CHECKLIST'
              Font = <>
              Title.Caption = 'ID'
              Width = 80
              Visible = True
              Precision = 0
              TextAlign = taCenter
              FieldType = ftInteger
              FlexRatio = 0
              Sort = False
              ImageHeader = 0
              Wrap = False
              Flex = False
              Colors = <>
              Images = <>
              Masks = <>
              CharCase = ccNormal
              BlobConfig.MimeType = bmtText
              BlobConfig.ShowType = btImageViewer
              ShowLabel = True
              Editor.EditType = etTFInteger
              Editor.Precision = 0
              Editor.Step = 0
              Editor.MaxLength = 100
              Editor.LookupFilterKey = 0
              Editor.LookupFilterDesc = 0
              Editor.PopupHeight = 400
              Editor.PopupWidth = 400
              Editor.CharCase = ccNormal
              Editor.LookupColumns = <>
              Editor.Enabled = False
              Editor.ReadOnly = True
              Editor.Filter = False
              Editor.ShowClearButton = False
              CheckedValue = 'S'
              UncheckedValue = 'N'
              HiperLink = False
              OnClick = 'IdChecklistClick'
              GUID = '{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}'
              WOwner = FrInterno
              WOrigem = EhNone
              EditorConstraint.CheckWhen = cwImmediate
              EditorConstraint.CheckType = ctExpression
              EditorConstraint.FocusOnError = False
              EditorConstraint.EnableUI = True
              EditorConstraint.Enabled = False
              EditorConstraint.FormCheck = True
              Empty = False
              MobileOpts.ShowMobile = False
              MobileOpts.Order = 0
              BoxSize = 0
              ImageSrcType = istSource
              IconReverseDirection = False
              FooterConfig.ColSpan = 0
              FooterConfig.TextAlign = taLeft
              FooterConfig.Enabled = False
              HeaderTextAlign = taCenter
              Priority = 0
            end
            item
              Expanded = False
              FieldName = 'DESCRICAO'
              Font = <>
              Title.Caption = 'Descri'#231#227'o'
              Width = 252
              Visible = True
              Precision = 0
              TextAlign = taLeft
              FieldType = ftString
              FlexRatio = 0
              Sort = False
              ImageHeader = 0
              Wrap = False
              Flex = True
              Colors = <>
              Images = <>
              Masks = <>
              CharCase = ccNormal
              BlobConfig.MimeType = bmtText
              BlobConfig.ShowType = btImageViewer
              ShowLabel = True
              Editor.EditType = etTFString
              Editor.Precision = 0
              Editor.Step = 0
              Editor.MaxLength = 100
              Editor.LookupFilterKey = 0
              Editor.LookupFilterDesc = 0
              Editor.PopupHeight = 400
              Editor.PopupWidth = 400
              Editor.CharCase = ccNormal
              Editor.LookupColumns = <>
              Editor.Enabled = False
              Editor.ReadOnly = False
              Editor.Filter = False
              Editor.ShowClearButton = False
              CheckedValue = 'S'
              UncheckedValue = 'N'
              HiperLink = False
              GUID = '{49696994-4A62-4930-8A5F-B50F20171B2F}'
              WOwner = FrInterno
              WOrigem = EhNone
              EditorConstraint.CheckWhen = cwImmediate
              EditorConstraint.CheckType = ctExpression
              EditorConstraint.FocusOnError = False
              EditorConstraint.EnableUI = True
              EditorConstraint.Enabled = False
              EditorConstraint.FormCheck = True
              Empty = False
              MobileOpts.ShowMobile = False
              MobileOpts.Order = 0
              BoxSize = 0
              ImageSrcType = istSource
              IconReverseDirection = False
              FooterConfig.ColSpan = 0
              FooterConfig.TextAlign = taLeft
              FooterConfig.Enabled = False
              HeaderTextAlign = taLeft
              Priority = 0
            end
            item
              Expanded = False
              FieldName = 'ATIVO'
              Font = <>
              Title.Caption = 'Ativo'
              Width = 51
              Visible = True
              Precision = 0
              TextAlign = taLeft
              FieldType = ftString
              FlexRatio = 0
              Sort = False
              ImageHeader = 0
              Wrap = False
              Flex = False
              Colors = <>
              Images = <
                item
                  Expression = 'ATIVO = '#39'S'#39
                  EvalType = etExpression
                  GUID = '{D4F1B10D-889F-4DED-8979-3C50EE62026C}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  ImageId = 7000105
                  OnClick = 'CheckClick'
                  Color = clBlack
                end
                item
                  Expression = '((ATIVO = '#39'N'#39') OR (ATIVO IS NULL))'
                  EvalType = etExpression
                  GUID = '{07122255-177E-4A53-AA1C-42BF670731FF}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  ImageId = 7000106
                  OnClick = 'UnCheckClick'
                  Color = clBlack
                end>
              Masks = <>
              CharCase = ccNormal
              BlobConfig.MimeType = bmtText
              BlobConfig.ShowType = btImageViewer
              ShowLabel = False
              Editor.EditType = etTFString
              Editor.Precision = 0
              Editor.Step = 0
              Editor.MaxLength = 100
              Editor.LookupFilterKey = 0
              Editor.LookupFilterDesc = 0
              Editor.PopupHeight = 400
              Editor.PopupWidth = 400
              Editor.CharCase = ccNormal
              Editor.LookupColumns = <>
              Editor.Enabled = False
              Editor.ReadOnly = False
              Editor.Filter = False
              Editor.ShowClearButton = False
              CheckedValue = 'S'
              UncheckedValue = 'N'
              HiperLink = False
              GUID = '{9A69EF6F-F418-49F7-B365-C3ABD20B833D}'
              WOwner = FrInterno
              WOrigem = EhNone
              EditorConstraint.CheckWhen = cwImmediate
              EditorConstraint.CheckType = ctExpression
              EditorConstraint.FocusOnError = False
              EditorConstraint.EnableUI = True
              EditorConstraint.Enabled = False
              EditorConstraint.FormCheck = True
              Empty = False
              MobileOpts.ShowMobile = False
              MobileOpts.Order = 0
              BoxSize = 0
              ImageSrcType = istSource
              IconReverseDirection = False
              FooterConfig.ColSpan = 0
              FooterConfig.TextAlign = taLeft
              FooterConfig.Enabled = False
              HeaderTextAlign = taLeft
              Priority = 0
            end>
        end
      end
    end
  end
  object tbChecklist: TFTable
    FieldDefs = <
      item
        Name = 'ID_CHECKLIST'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Checklist'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OBSERVACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Observa'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MONTADORA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Montadora'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ATIVO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Ativo'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'MOB_CHECKLIST'
    Cursor = 'MOB_CHECKLIST'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '342012;34201'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
    Left = 384
    Top = 8
  end
  object tbConcessionariaTipo: TFTable
    FieldDefs = <
      item
        Name = 'COD_TIPO_CONCESSIONARIA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Tipo Concessionaria'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CONCESSIONARIA_TIPO'
    Cursor = 'CONCESSIONARIA_TIPO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '342012;34202'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
    Left = 440
    Top = 10
  end
end
