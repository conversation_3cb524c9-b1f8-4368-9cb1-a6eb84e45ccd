package freedom.bytecode.form;
import freedom.bytecode.form.wizard.FrmChecklistImportacaoW;
import freedom.client.event.Event;
import freedom.data.DataException;
import freedom.util.CrmServiceUtil;
import freedom.util.EmpresaUtil;
import freedom.util.pkg.PkgCrmPartsRNA;
import freedom.util.pkg.PkgCrmServiceChecklistRNA;

public class FrmChecklistImportacaoA extends FrmChecklistImportacaoW {
    private static final long serialVersionUID = 20130827081850L;
    private final PkgCrmPartsRNA pkCrmPartsRna = new PkgCrmPartsRNA();
    private  final PkgCrmServiceChecklistRNA pkgCrmServiceChecklistRna = new PkgCrmServiceChecklistRNA();

    int empresaLogada = EmpresaUtil.getCodEmpresaUserLogged();

    public FrmChecklistImportacaoA() {
        filtrarTabelas();
    }

    public void filtrarTabelas(){
        try {
            double tipoRevendaSel;
            tipoRevendaSel = Integer.parseInt(pkCrmPartsRna.getParametro((double) empresaLogada, "PARM_SYS", "TIPO_CONCESSIONARIA"));
            rn.filtrarCheckList(tipoRevendaSel);
            rn.filtrarConcessionariaTipo(tipoRevendaSel);
        } catch (DataException e) {
            CrmServiceUtil.showError("Erro ao filtrar tabelas", e);
        }
    }

    public void recarregarRegistro(){
        try {
            rn.recarregarRegistro();
        } catch (DataException e) {
            CrmServiceUtil.showError("Erro ao recarregar tabelas", e);
        }
    }

    @Override
    public void gridChecklistCheckClick(Event<Object> event) {
        try {
            double idChecklist = rn.tbChecklistGetId();
            String retFuncao = pkgCrmServiceChecklistRna.incluirChecklist(idChecklist, "N");

            if (!retFuncao.equals("S")){
                throw new DataException(retFuncao);
            }
            //filtrarTabelas();
            recarregarRegistro();
        } catch (DataException e) {
            CrmServiceUtil.showError("Não foi possivel ativar o checklist",e);
        }
    }

    @Override
    public void gridChecklistUnCheckClick(Event<Object> event) {
        try {
            double idChecklist = rn.tbChecklistGetId();
            String retFuncao = pkgCrmServiceChecklistRna.incluirChecklist(idChecklist, "S");
            if (!retFuncao.equals("S")){
                throw new DataException(retFuncao);
            }
            //filtrarTabelas();
            recarregarRegistro();
        } catch (DataException e) {
            CrmServiceUtil.showError("Não foi desativar ativar o checklist",e);
        }
    }

    @Override
    public void gridChecklistIdChecklistClick(Event<Object> event) {
        try {
            // Obter informações do checklist atual
            double idChecklist = rn.tbChecklistGetId();
            String descricao = rn.tbChecklist.getDESCRICAO().asString();
            String observacao = rn.tbChecklist.getOBSERVACAO().asString();
            String ativo = rn.tbChecklist.getATIVO().asString();

            // Montar a mensagem com as informações do checklist
            StringBuilder mensagem = new StringBuilder();
            mensagem.append("ID: ").append((int)idChecklist).append("\n");
            mensagem.append("Descrição: ").append(descricao).append("\n");
            mensagem.append("Status: ").append("S".equals(ativo) ? "Ativo" : "Inativo").append("\n");

            if (observacao != null && !observacao.trim().isEmpty()) {
                mensagem.append("\nObservações:\n").append(observacao);
            } else {
                mensagem.append("\nNenhuma observação cadastrada.");
            }

            // Exibir a mensagem
            CrmServiceUtil.showMessage("Informações do Checklist", mensagem.toString());

        } catch (DataException e) {
            CrmServiceUtil.showError("Erro ao obter informações do checklist", e);
        }
    }

}
